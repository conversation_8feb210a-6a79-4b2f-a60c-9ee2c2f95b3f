#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成历史数据迁移脚本
将现有用户的 generation_history 数据从 users.json 迁移到独立的 generation_history.json 文件中
"""

import json
import os
import shutil
from datetime import datetime
from generation_history import GenerationHistoryManager

def backup_files():
    """备份原始文件"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 备份 users.json
    if os.path.exists('users.json'):
        backup_users = f'users.json.backup_before_migration_{timestamp}'
        shutil.copy2('users.json', backup_users)
        print(f"已备份 users.json 到 {backup_users}")
    
    # 备份现有的 generation_history.json（如果存在）
    if os.path.exists('generation_history.json'):
        backup_history = f'generation_history.json.backup_before_migration_{timestamp}'
        shutil.copy2('generation_history.json', backup_history)
        print(f"已备份 generation_history.json 到 {backup_history}")
    
    return timestamp

def load_users_data():
    """加载用户数据"""
    if not os.path.exists('users.json'):
        print("错误：users.json 文件不存在")
        return None
    
    try:
        with open('users.json', 'r', encoding='utf-8') as f:
            users_data = json.load(f)
        print(f"成功加载用户数据，包含 {len(users_data)} 个用户")
        return users_data
    except Exception as e:
        print(f"加载用户数据失败: {e}")
        return None

def migrate_generation_history():
    """迁移生成历史数据"""
    print("开始迁移生成历史数据...")
    
    # 备份文件
    backup_timestamp = backup_files()
    
    # 加载用户数据
    users_data = load_users_data()
    if users_data is None:
        return False
    
    # 初始化生成历史管理器
    history_manager = GenerationHistoryManager()
    
    # 统计信息
    total_users = len(users_data)
    users_with_history = 0
    total_records_migrated = 0
    users_cleaned = []
    
    print(f"开始处理 {total_users} 个用户的数据...")
    
    # 遍历所有用户
    for username, user_data in users_data.items():
        if 'generation_history' in user_data and user_data['generation_history']:
            users_with_history += 1
            history_records = user_data['generation_history']
            records_count = len(history_records)
            
            print(f"处理用户 {username}: {records_count} 条记录")
            
            # 将记录添加到新的历史管理器中
            if username not in history_manager.history_data:
                history_manager.history_data[username] = []
            
            # 直接复制记录（保持原有格式）
            history_manager.history_data[username].extend(history_records)
            total_records_migrated += records_count
            
            # 从用户数据中移除 generation_history 字段
            del user_data['generation_history']
            users_cleaned.append(username)
            
        elif 'generation_history' in user_data:
            # 用户有 generation_history 字段但为空，也要移除
            del user_data['generation_history']
            users_cleaned.append(username)
    
    # 保存迁移后的历史数据
    print("保存生成历史数据...")
    if history_manager.save_history():
        print(f"成功保存生成历史数据到 generation_history.json")
    else:
        print("保存生成历史数据失败")
        return False
    
    # 保存清理后的用户数据
    print("保存清理后的用户数据...")
    try:
        with open('users.json', 'w', encoding='utf-8') as f:
            json.dump(users_data, f, ensure_ascii=False, indent=2)
        print("成功保存清理后的用户数据")
    except Exception as e:
        print(f"保存用户数据失败: {e}")
        return False
    
    # 输出迁移统计信息
    print("\n" + "="*50)
    print("迁移完成！统计信息：")
    print(f"总用户数: {total_users}")
    print(f"有历史记录的用户数: {users_with_history}")
    print(f"清理了 generation_history 字段的用户数: {len(users_cleaned)}")
    print(f"迁移的总记录数: {total_records_migrated}")
    print(f"备份时间戳: {backup_timestamp}")
    print("="*50)
    
    return True

def verify_migration():
    """验证迁移结果"""
    print("\n验证迁移结果...")
    
    # 检查 users.json 中是否还有 generation_history 字段
    try:
        with open('users.json', 'r', encoding='utf-8') as f:
            users_data = json.load(f)
        
        users_with_history = 0
        for username, user_data in users_data.items():
            if 'generation_history' in user_data:
                users_with_history += 1
        
        if users_with_history == 0:
            print("✓ users.json 中已无 generation_history 字段")
        else:
            print(f"✗ users.json 中仍有 {users_with_history} 个用户包含 generation_history 字段")
    except Exception as e:
        print(f"验证 users.json 失败: {e}")
    
    # 检查 generation_history.json
    try:
        history_manager = GenerationHistoryManager()
        stats = history_manager.get_history_statistics()
        print(f"✓ generation_history.json 包含 {stats['total_users']} 个用户的 {stats['total_records']} 条记录")
    except Exception as e:
        print(f"验证 generation_history.json 失败: {e}")

def main():
    """主函数"""
    print("生成历史数据迁移脚本")
    print("="*50)
    
    # 检查必要文件
    if not os.path.exists('users.json'):
        print("错误：users.json 文件不存在，无法进行迁移")
        return
    
    # 询问用户确认
    print("此脚本将：")
    print("1. 备份 users.json 和 generation_history.json（如果存在）")
    print("2. 将所有用户的 generation_history 数据迁移到独立的 generation_history.json 文件")
    print("3. 从 users.json 中移除所有 generation_history 字段")
    print()
    
    confirm = input("确认执行迁移？(y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("迁移已取消")
        return
    
    # 执行迁移
    if migrate_generation_history():
        verify_migration()
        print("\n迁移成功完成！")
        print("请重启应用程序以使用新的生成历史管理系统。")
    else:
        print("\n迁移失败！请检查错误信息并重试。")

if __name__ == '__main__':
    main()
