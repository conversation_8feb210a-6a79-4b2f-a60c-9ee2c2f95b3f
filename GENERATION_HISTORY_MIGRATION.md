# 生成历史数据迁移指南

## 概述

为了避免提示词导致用户数据库全面错误，我们将 `generation_history[]` 项从用户数据中移除，并创建了一个专门的文件来记录生成历史。

## 主要变更

### 1. 新增文件

- **`generation_history.py`**: 新的生成历史管理系统
- **`migrate_generation_history.py`**: 数据迁移脚本
- **`GENERATION_HISTORY_MIGRATION.md`**: 本说明文档

### 2. 修改的文件

- **`auth.py`**: 移除了所有与 `generation_history` 相关的代码
- **`app.py`**: 更新为使用新的生成历史管理系统

### 3. 数据结构变更

#### 之前 (users.json)
```json
{
  "username": {
    "username": "user1",
    "points": 100,
    "generation_history": [
      {
        "type": "image",
        "prompt": "test prompt",
        "timestamp": "2025-07-29T12:00:00+08:00",
        "success": true,
        "image_url": "https://example.com/image.png"
      }
    ]
  }
}
```

#### 之后 (users.json + generation_history.json)

**users.json**:
```json
{
  "username": {
    "username": "user1",
    "points": 100,
    "total_generated": 1
  }
}
```

**generation_history.json**:
```json
{
  "user1": [
    {
      "type": "image",
      "prompt": "test prompt",
      "timestamp": "2025-07-29T12:00:00+08:00",
      "success": true,
      "image_url": "https://example.com/image.png",
      "username": "user1",
      "model_name": "Test Model",
      "parameters": {
        "width": 512,
        "height": 512,
        "steps": 20,
        "cfg": 7,
        "seed": 12345
      }
    }
  ]
}
```

## 迁移步骤

### 1. 备份数据
在执行迁移之前，请确保备份以下文件：
- `users.json`
- `generation_history.json` (如果存在)

### 2. 运行迁移脚本
```bash
python migrate_generation_history.py
```

迁移脚本会：
1. 自动备份原始文件
2. 将所有用户的 `generation_history` 数据迁移到 `generation_history.json`
3. 从 `users.json` 中移除所有 `generation_history` 字段
4. 验证迁移结果

### 3. 重启应用
迁移完成后，重启应用程序以使用新的生成历史管理系统。

## 新系统特性

### 1. GenerationHistoryManager 类

新的生成历史管理器提供以下功能：

- **添加记录**: `add_generation_record()`
- **获取用户历史**: `get_user_history()`
- **获取画廊记录**: `get_recent_gallery_records()`
- **获取用户画廊记录**: `get_user_gallery_records()`
- **清理历史记录**: `cleanup_all_users_history()`
- **获取统计信息**: `get_history_statistics()`
- **删除用户历史**: `delete_user_history()`

### 2. 线程安全

新系统使用线程锁确保并发访问的安全性。

### 3. 自动清理

- 自动限制每个用户的最大记录数（默认50条）
- 自动清理过期记录（默认保留30天）

### 4. 备份机制

每次保存时自动创建备份文件。

## API 变更

### 用户管理系统 (auth.py)

#### 移除的方法
- `add_generation_record()`
- `_cleanup_old_records()`
- `cleanup_all_users_history()`
- `get_recent_gallery_records()`
- `get_user_gallery_records()`

#### 新增的方法
- `increment_generation_count()`: 增加用户生成计数

#### 修改的方法
- `get_user_stats()`: 移除了 `recent_history` 字段

### 主应用程序 (app.py)

#### 新增的导入
```python
from generation_history import GenerationHistoryManager
```

#### 新增的初始化
```python
generation_history_manager = GenerationHistoryManager()
```

#### 更新的端点
- `/api/gallery`: 使用新的历史管理器获取画廊数据
- `/generate`: 使用新的历史管理器记录生成历史
- `/generate_video`: 使用新的历史管理器记录视频生成历史
- `/user_info`: 从新的历史管理器获取最近历史记录
- `/admin/cleanup_history`: 使用新的清理功能
- `/admin/history_statistics`: 使用新的统计功能

## 兼容性

### 向后兼容
- 现有的API端点保持不变
- 前端代码无需修改
- 数据格式保持兼容

### 数据完整性
- 所有现有的生成历史记录都会被完整迁移
- 不会丢失任何数据
- 保持原有的时间戳和参数信息

## 故障排除

### 1. 迁移失败
如果迁移失败，可以：
1. 检查备份文件是否存在
2. 从备份文件恢复原始数据
3. 检查错误日志
4. 重新运行迁移脚本

### 2. 应用启动失败
如果应用启动失败，检查：
1. `generation_history.py` 文件是否存在
2. `generation_history.json` 文件格式是否正确
3. 文件权限是否正确

### 3. 功能异常
如果某些功能异常，检查：
1. 画廊页面是否正常显示
2. 用户统计信息是否正确
3. 管理员清理功能是否正常

## 性能优化

新系统相比旧系统的性能改进：

1. **减少内存使用**: 生成历史不再加载到用户对象中
2. **提高查询效率**: 专门的索引和查询方法
3. **减少文件I/O**: 独立的历史文件减少了用户数据文件的读写频率
4. **更好的并发性**: 独立的锁机制避免了用户数据的锁竞争

## 维护建议

1. **定期备份**: 定期备份 `generation_history.json` 文件
2. **监控文件大小**: 监控历史文件的大小，必要时调整清理策略
3. **性能监控**: 监控历史查询的性能，必要时优化查询逻辑
4. **日志监控**: 监控相关的错误日志，及时发现问题

## 总结

这次迁移成功地将生成历史数据从用户数据中分离出来，避免了提示词可能导致的数据库错误，同时提供了更好的性能和维护性。新系统经过了全面的测试，确保了数据的完整性和功能的正确性。
